# Design Generation Shimmer Service

## Overview

The `DesignGenerationShimmerService` provides a unified shimmer loading experience for design generation API calls. It manages loading states, error handling, and integrates with the chat window to display shimmer animations while the `/design-generation` API is processing.

## Key Features

- **Shimmer Loading**: Shows shimmer animation in chat window during API processing
- **State Management**: Tracks generation progress, completion, and error states
- **Error Handling**: Graceful error handling with user-friendly messages
- **Chat Integration**: Seamlessly integrates with chat window for shimmer display
- **Event Streams**: Provides observables for components to react to state changes

## Usage

### Basic Usage

```typescript
import { DesignGenerationShimmerService } from './design-generation-shimmer.service';

// Inject the service
constructor(private designGenerationShimmerService: DesignGenerationShimmerService) {}

// Start design generation with shimmer
const request = {
  prompt: 'Create a modern dashboard',
  device: 'web',
  image: [],
  project_type: 'wireframe_generation'
};

this.designGenerationShimmerService.startDesignGenerationWithShimmer(
  request,
  this.chatWindow, // Chat window component for shimmer display
  'unique-message-id' // Optional message ID
).subscribe({
  next: (response) => {
    console.log('Design generation completed:', response);
    // Handle successful response
  },
  error: (error) => {
    console.error('Design generation failed:', error);
    // Handle error
  }
});
```

### State Observables

```typescript
// Subscribe to generation state
this.designGenerationShimmerService.isGenerating$.subscribe(isGenerating => {
  console.log('Is generating:', isGenerating);
});

this.designGenerationShimmerService.hasError$.subscribe(hasError => {
  console.log('Has error:', hasError);
});

this.designGenerationShimmerService.errorMessage$.subscribe(errorMessage => {
  console.log('Error message:', errorMessage);
});
```

### Event Streams

```typescript
// Listen to generation events
this.designGenerationShimmerService.generationStarted$.subscribe(event => {
  console.log('Generation started:', event);
});

this.designGenerationShimmerService.generationCompleted$.subscribe(event => {
  console.log('Generation completed:', event);
});

this.designGenerationShimmerService.generationFailed$.subscribe(event => {
  console.log('Generation failed:', event);
});
```

## Integration with Code Window

The service is integrated into the `CodeWindowComponent` for seamless design generation:

```typescript
// In code-window.component.ts
private initiateParallelUIDesignAPICalls(uiDesignData: any): void {
  // Build API request
  const apiRequest = this.generateUIDesignService.buildAPIRequest(images);

  // Use shimmer service for unified loading experience
  this.designGenerationShimmerService.startDesignGenerationWithShimmer(
    apiRequest,
    this.chatWindow,
    this.currentActiveMessageId
  ).subscribe({
    next: (response) => this.handleUIDesignSuccess(response),
    error: (error) => this.handleUIDesignFailure(error)
  });
}
```

## Testing

### Browser Console Testing

For development testing, the service can be tested via browser console:

```javascript
// Test the shimmer functionality
window.codeWindowComponent.testDesignGenerationShimmer();
```

### Manual Testing Steps

1. Navigate to the UI Design generation page
2. Open browser console
3. Run the test command above
4. Observe shimmer animation in chat window
5. Check console logs for success/error messages

## API Integration

The service integrates with the `/design-generation` API endpoint:

- **Endpoint**: `POST /design-generation/design-generation`
- **Request Format**: `GenerateUIDesignRequest`
- **Response Format**: `GenerateUIDesignResponse`
- **Timeout**: No client-side timeout (server determines timeout)

## Error Handling

The service provides comprehensive error handling:

- **Network Errors**: Displays user-friendly error messages
- **API Errors**: Extracts error messages from response
- **Timeout Errors**: Handles long-running API calls gracefully
- **Chat Integration**: Replaces shimmer with error message in chat

## State Management

The service maintains the following states:

- `isGenerating`: Boolean indicating if generation is in progress
- `isComplete`: Boolean indicating if generation completed successfully
- `hasError`: Boolean indicating if an error occurred
- `errorMessage`: String containing error message (if any)
- `currentRequest`: The current API request being processed
- `currentResponse`: The response from the API (if successful)

## Dependencies

- `GenerateUIDesignService`: For making API calls
- `ToastService`: For displaying user notifications
- `createLogger`: For logging functionality
- Angular RxJS: For reactive programming patterns

## Best Practices

1. **Always provide chat window component** for shimmer display
2. **Use unique message IDs** for tracking multiple generations
3. **Subscribe to error states** for proper error handling
4. **Clean up subscriptions** using `takeUntilDestroyed`
5. **Test in development** using the browser console test method
