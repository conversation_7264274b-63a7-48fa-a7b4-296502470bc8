import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription, timer } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, tap, switchMap, catchError } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { GenerateUIDesignService, GenerateUIDesignRequest, GenerateUIDesignResponse } from './generate-ui-design.service';
import { ToastService } from './toast.service';

/**
 * Design Generation Shimmer Service
 *
 * This service manages shimmer loading states for design generation API calls.
 * It provides a unified interface for showing shimmer loading indicators while
 * the design generation API is processing, similar to SSE-based regeneration
 * but for one-shot API calls.
 *
 * Key Features:
 * - Shows shimmer loading during design generation API processing
 * - Manages loading states and error handling
 * - Provides observables for UI components to react to state changes
 * - Integrates with chat window for shimmer display
 * - Handles API timeouts and error recovery
 */
@Injectable({
  providedIn: 'root'
})
export class DesignGenerationShimmerService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('DesignGenerationShimmerService');

  // Core services
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  private readonly toastService = inject(ToastService);

  // State management
  private readonly isGeneratingSubject = new BehaviorSubject<boolean>(false);
  private readonly isCompleteSubject = new BehaviorSubject<boolean>(false);
  private readonly hasErrorSubject = new BehaviorSubject<boolean>(false);
  private readonly errorMessageSubject = new BehaviorSubject<string | null>(null);
  private readonly currentRequestSubject = new BehaviorSubject<GenerateUIDesignRequest | null>(null);
  private readonly currentResponseSubject = new BehaviorSubject<GenerateUIDesignResponse | null>(null);

  // Event streams
  private readonly generationStartedSubject = new Subject<{
    request: GenerateUIDesignRequest;
    messageId?: string;
  }>();
  private readonly generationCompletedSubject = new Subject<{
    response: GenerateUIDesignResponse;
    request: GenerateUIDesignRequest;
    messageId?: string;
  }>();
  private readonly generationFailedSubject = new Subject<{
    error: any;
    request: GenerateUIDesignRequest;
    messageId?: string;
  }>();

  // Public observables
  readonly isGenerating$ = this.isGeneratingSubject.asObservable();
  readonly isComplete$ = this.isCompleteSubject.asObservable();
  readonly hasError$ = this.hasErrorSubject.asObservable();
  readonly errorMessage$ = this.errorMessageSubject.asObservable();
  readonly currentRequest$ = this.currentRequestSubject.asObservable();
  readonly currentResponse$ = this.currentResponseSubject.asObservable();

  // Event observables
  readonly generationStarted$ = this.generationStartedSubject.asObservable();
  readonly generationCompleted$ = this.generationCompletedSubject.asObservable();
  readonly generationFailed$ = this.generationFailedSubject.asObservable();

  // Chat window component reference for shimmer display
  private chatWindowComponent: any = null;
  private currentMessageId: string | null = null;

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    this.logger.info('🔧 Design Generation Shimmer Service initialized');
    this.setupCleanup();
  }

  /**
   * Setup cleanup on destroy
   */
  private setupCleanup(): void {
    this.destroyRef.onDestroy(() => {
      this.subscriptions.unsubscribe();
      this.logger.info('🧹 Design Generation Shimmer Service cleaned up');
    });
  }

  /**
   * Start design generation with shimmer loading
   * @param request - The design generation request
   * @param chatWindowComponent - Optional chat window component for shimmer display
   * @param messageId - Optional message ID for tracking
   * @returns Observable with the generation response
   */
  startDesignGenerationWithShimmer(
    request: GenerateUIDesignRequest,
    chatWindowComponent?: any,
    messageId?: string
  ): Observable<GenerateUIDesignResponse> {
    this.logger.info('🚀 Starting design generation with shimmer loading:', {
      prompt: request.prompt.substring(0, 100) + '...',
      device: request.device,
      hasImages: request.image.length > 0,
      messageId
    });

    // Store references
    this.chatWindowComponent = chatWindowComponent;
    this.currentMessageId = messageId;

    // Reset state
    this.resetState();

    // Set generating state
    this.isGeneratingSubject.next(true);
    this.currentRequestSubject.next(request);

    // Show shimmer in chat window if available
    if (this.chatWindowComponent && this.chatWindowComponent.addAIMessageWithShimmer) {
      const shimmerMessageId = messageId || this.generateMessageId();
      this.currentMessageId = shimmerMessageId;
      this.chatWindowComponent.addAIMessageWithShimmer(shimmerMessageId);
      this.logger.info('🔄 Added shimmer loading card to chat window:', shimmerMessageId);
    }

    // Emit generation started event
    this.generationStartedSubject.next({ request, messageId });

    // Make the API call
    return this.generateUIDesignService.generateUIDesign(request).pipe(
      tap(response => {
        this.logger.info('✅ Design generation completed successfully:', {
          wireframeCount: response.wireframes?.length || 0,
          projectId: response.project_id,
          commitHash: response.commit_hash
        });

        // Update state
        this.isGeneratingSubject.next(false);
        this.isCompleteSubject.next(true);
        this.currentResponseSubject.next(response);

        // Replace shimmer with actual content
        this.replaceShimmerWithContent(response);

        // Emit completion event
        this.generationCompletedSubject.next({ response, request, messageId });
      }),
      catchError(error => {
        this.logger.error('❌ Design generation failed:', error);

        // Update error state
        this.isGeneratingSubject.next(false);
        this.hasErrorSubject.next(true);
        this.errorMessageSubject.next(this.extractErrorMessage(error));

        // Replace shimmer with error message
        this.replaceShimmerWithError(error);

        // Emit failure event
        this.generationFailedSubject.next({ error, request, messageId });

        // Show toast error
        this.toastService.error('Design generation failed. Please try again.');

        // Re-throw error for caller to handle
        throw error;
      }),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Reset all state to initial values
   */
  private resetState(): void {
    this.isGeneratingSubject.next(false);
    this.isCompleteSubject.next(false);
    this.hasErrorSubject.next(false);
    this.errorMessageSubject.next(null);
    this.currentRequestSubject.next(null);
    this.currentResponseSubject.next(null);
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `design-gen-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Replace shimmer with actual content
   */
  private replaceShimmerWithContent(response: GenerateUIDesignResponse): void {
    if (this.chatWindowComponent && this.currentMessageId) {
      // Create a summary message about the generated wireframes
      const wireframeCount = response.wireframes?.length || 0;
      const summaryText = `Generated ${wireframeCount} wireframe${wireframeCount !== 1 ? 's' : ''} for your design. You can view them in the preview area.`;

      // Replace shimmer with content
      if (this.chatWindowComponent.replaceShimmerWithContent) {
        this.chatWindowComponent.replaceShimmerWithContent(this.currentMessageId, summaryText);
        this.logger.info('✅ Replaced shimmer with content:', this.currentMessageId);
      }
    }
  }

  /**
   * Replace shimmer with error message
   */
  private replaceShimmerWithError(error: any): void {
    if (this.chatWindowComponent && this.currentMessageId) {
      const errorMessage = this.extractErrorMessage(error);
      const errorText = `Design generation failed: ${errorMessage}. Please try again.`;

      // Replace shimmer with error
      if (this.chatWindowComponent.replaceShimmerWithContent) {
        this.chatWindowComponent.replaceShimmerWithContent(this.currentMessageId, errorText);
        this.logger.info('❌ Replaced shimmer with error:', this.currentMessageId);
      }
    }
  }

  /**
   * Extract error message from error object
   */
  private extractErrorMessage(error: any): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unexpected error occurred';
  }

  /**
   * Get current generation state
   */
  isCurrentlyGenerating(): boolean {
    return this.isGeneratingSubject.value;
  }

  /**
   * Get current request
   */
  getCurrentRequest(): GenerateUIDesignRequest | null {
    return this.currentRequestSubject.value;
  }

  /**
   * Get current response
   */
  getCurrentResponse(): GenerateUIDesignResponse | null {
    return this.currentResponseSubject.value;
  }

  /**
   * Clear current state
   */
  clearState(): void {
    this.resetState();
    this.chatWindowComponent = null;
    this.currentMessageId = null;
    this.logger.info('🧹 Design generation state cleared');
  }
}
